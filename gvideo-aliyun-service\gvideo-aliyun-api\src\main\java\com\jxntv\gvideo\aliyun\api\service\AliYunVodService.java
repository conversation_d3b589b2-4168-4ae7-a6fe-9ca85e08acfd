package com.jxntv.gvideo.aliyun.api.service;

import java.util.List;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.vod.model.v20170321.CreateUploadImageResponse;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.GetImageInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetMezzanineInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoListResponse;
import com.aliyuncs.vod.model.v20170321.ListLiveRecordVideoResponse;
import com.aliyuncs.vod.model.v20170321.RefreshUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.UploadMediaByURLResponse;
import com.jxntv.gvideo.aliyun.sdk.dto.AliyunUploadMediaByURLDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.AliyunVodPageSearchDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.ListLiveRecordVideoSearchDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodVideoPlayDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodWatermarkAddDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodWatermarkImageCreateDTO;

/**
 * Created on 2020-03-03
 */
public interface AliYunVodService {

    CreateUploadVideoResponse createUploadVideo(String fileName) throws ClientException;

    RefreshUploadVideoResponse refreshUploadVideo(String videoId) throws ClientException;

    CreateUploadImageResponse createUploadImage(String fileName, String ext) throws ClientException;

    String getVideoStatus(String videoId) throws ClientException;

    String addVideoWatermark(VodWatermarkAddDTO addWatermarkDTO) throws ClientException;

    String createWatermarkImage(VodWatermarkImageCreateDTO dto);


    String getVideoPlayUrl(String videoId) throws ClientException;

    String getSourceVideoUrl(String videoId) throws ClientException;

    GetPlayInfoResponse getAudioPlayInfo(String audioId) throws ClientException;

    GetImageInfoResponse getImageInfo(String imageId) throws ClientException;

    /**
     * 返回视频详情
     *
     * @param ossId
     * @return
     */
    GetMezzanineInfoResponse.Mezzanine getMezzanine(String ossId);

    /**
     * 获取直转点视频列表
     *
     * @param searchDTO
     * @return
     */
    ListLiveRecordVideoResponse listLiveRecordVideo(ListLiveRecordVideoSearchDTO searchDTO) throws ClientException;

    /**
     * 分页查询视频点播文件
     * 
     * @param dto
     * @return
     */
    GetVideoListResponse vodPageSearch(AliyunVodPageSearchDTO dto);

    /**
     * 通过视频ID删除视频源文件
     * 
     * @param videoIdList
     * @return
     */
    boolean deleteMezzanines(List<String> videoIdList);

    VodVideoPlayDTO getVideoPlayUrlNew(String videoId) throws ClientException;

    UploadMediaByURLResponse uploadMediaByURL(AliyunUploadMediaByURLDTO dto) throws Exception;
}
