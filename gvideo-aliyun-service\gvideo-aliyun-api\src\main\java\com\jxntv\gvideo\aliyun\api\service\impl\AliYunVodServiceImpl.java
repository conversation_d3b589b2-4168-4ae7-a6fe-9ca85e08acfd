package com.jxntv.gvideo.aliyun.api.service.impl;

import java.awt.Font;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.vod.model.v20170321.CreateUploadImageRequest;
import com.aliyuncs.vod.model.v20170321.CreateUploadImageResponse;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoRequest;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.DeleteMezzaninesRequest;
import com.aliyuncs.vod.model.v20170321.DeleteMezzaninesResponse;
import com.aliyuncs.vod.model.v20170321.GetImageInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetImageInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetMezzanineInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetMezzanineInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetVideoInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoListRequest;
import com.aliyuncs.vod.model.v20170321.GetVideoListResponse;
import com.aliyuncs.vod.model.v20170321.ListLiveRecordVideoRequest;
import com.aliyuncs.vod.model.v20170321.ListLiveRecordVideoResponse;
import com.aliyuncs.vod.model.v20170321.RefreshUploadVideoRequest;
import com.aliyuncs.vod.model.v20170321.RefreshUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.SubmitTranscodeJobsRequest;
import com.aliyuncs.vod.model.v20170321.SubmitTranscodeJobsResponse;
import com.aliyuncs.vod.model.v20170321.UploadMediaByURLRequest;
import com.aliyuncs.vod.model.v20170321.UploadMediaByURLResponse;
import com.google.gson.Gson;
import com.jxntv.gvideo.aliyun.api.service.AliYunVodService;
import com.jxntv.gvideo.aliyun.api.utils.WatermarkUtil;
import com.jxntv.gvideo.aliyun.sdk.dto.AliyunUploadMediaByURLDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.AliyunVodPageSearchDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.ListLiveRecordVideoSearchDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodVideoPlayDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodWatermarkAddDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodWatermarkImageCreateDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * Created on 2020-03-03
 */
@Service
@Slf4j
public class AliYunVodServiceImpl implements AliYunVodService {
    private final String LIST_LIVE_RECORD_ACTION = "ListLiveRecordVideo";
    private final String UTC_TIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    private static Set<String> audioSuffix = new HashSet<>();

    static {
        audioSuffix.add(".mp3");
        audioSuffix.add(".aac");
        audioSuffix.add(".ac3");
        audioSuffix.add(".acm");
        audioSuffix.add(".amr");
        audioSuffix.add(".ape");
        audioSuffix.add(".caf");
        audioSuffix.add(".flac");
        audioSuffix.add(".m4a");
        audioSuffix.add(".wav");
        audioSuffix.add(".wma");
    }

    @Value("${aliyun.vod.region}")
    private String regionId;

    @Value("${aliyun.vod.AccessKeyID}")
    private String accessKeyId;

    @Value("${aliyun.vod.AccessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.vod.templateGroupId}")
    private String templateGroupId;
    @Value("${aliyun.vod.soundTemplateId}")
    private String soundTemplateId;

    @Resource
    private ResourceLoader resourceLoader;

    @Override
    public CreateUploadVideoResponse createUploadVideo(String fileName) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        request.setTitle(fileName);
        request.setFileName(fileName);
        boolean isAudio = false;
        for (String suffix : audioSuffix) {
            if (fileName.endsWith(suffix)) {
                isAudio = true;
                break;
            }
        }
        log.info("templateGroupId:" + templateGroupId);
        log.info("soundTemplateId:" + soundTemplateId);
        if (!isAudio) {
            request.setTemplateGroupId(templateGroupId);
        } else {
            request.setTemplateGroupId(soundTemplateId);
        }
        return client.getAcsResponse(request);
    }

    @Override
    public RefreshUploadVideoResponse refreshUploadVideo(String videoId) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        RefreshUploadVideoRequest request = new RefreshUploadVideoRequest();
        request.setVideoId(videoId);
        return client.getAcsResponse(request);
    }

    @Override
    public CreateUploadImageResponse createUploadImage(String fileName, String ext) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        CreateUploadImageRequest request = new CreateUploadImageRequest();
        request.setImageType("default");
        request.setImageExt(ext);
        request.setTitle(fileName);
        return client.getAcsResponse(request);
    }

    @Override
    public String getVideoStatus(String videoId) throws ClientException {

        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);

        GetVideoInfoRequest statusRequest = new GetVideoInfoRequest();
        statusRequest.setVideoId(videoId);
        GetVideoInfoResponse acsResponse = client.getAcsResponse(statusRequest);
        return acsResponse.getVideo().getStatus();
    }

    @Override
    public String addVideoWatermark(VodWatermarkAddDTO dto) throws ClientException {
        // 设置覆盖参数
        String watermarkId = dto.getWatermarkId();
        String fileUrl = dto.getWatermarkImageUrl();
        JSONObject overrideParams = new JSONObject();
        JSONArray watermarks = new JSONArray();
        JSONObject watermark = new JSONObject();
        watermark.put("WatermarkId", watermarkId);
        watermark.put("FileUrl", fileUrl);
        watermarks.add(watermark);
        overrideParams.put("Watermarks", watermarks);

        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);


        SubmitTranscodeJobsRequest request = new SubmitTranscodeJobsRequest();
        request.setVideoId(dto.getVideoId());
        request.setTemplateGroupId(dto.getTemplateGroupId());
        request.setOverrideParams(overrideParams.toJSONString());
        SubmitTranscodeJobsResponse acsResponse = client.getAcsResponse(request);
        return acsResponse.getTranscodeTaskId();
    }


    @Override
    public String createWatermarkImage(VodWatermarkImageCreateDTO dto) {
        String content = dto.getContent();
        BufferedImage image = WatermarkUtil.create(content, createFont());

        String url = "";

        // 创建一个ByteArrayOutputStream，用于接收图像数据
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        // 将BufferedImage写入ByteArrayOutputStream
        try {

            ImageIO.write(image, "png", byteArrayOutputStream);

            // 将ByteArrayOutputStream转换为字节数组
            byte[] imageBytes = byteArrayOutputStream.toByteArray();

            // 使用ByteArrayInputStream将字节数组转换为InputStream
            InputStream inputStream = new ByteArrayInputStream(imageBytes);

            url = uploadWatermarkImage(content, inputStream);

        } catch (Exception e) {
            log.error("构建水印图片错误", e);
        }

        return url;
    }


    private Font createFont() {

        Font font;

        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:" + "/fonts/STXIHEI.TTF");

        try (InputStream fontStream = resource.getInputStream()) {
            // 加载系统字体
            font = Font.createFont(Font.TRUETYPE_FONT, fontStream);
            // 设置字体大小
            font = font.deriveFont(Font.PLAIN, 16f);

        } catch (Exception e) {

            log.error("加载字体失败：", e);

            // 如果加载自定义字体失败,使用系统默认字体
            font = new Font("SansSerif", Font.PLAIN, 16);

        }

        return font;
    }


    /**
     * 辅助媒资上传接口，流式上传示例（支持文件流和网络流）
     */
    private String uploadWatermarkImage(String title, InputStream fileStream) {
        return WatermarkUtil.upload(title, regionId, accessKeyId, accessKeySecret, fileStream);

    }


    /**
     * 获取视频地址
     *
     * @param videoId
     * @return
     * @throws ClientException
     */
    @Override
    public String getVideoPlayUrl(String videoId) throws ClientException {
        // 由于视频会存在转码失败的情况，需要先获取视频状态来判断
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        // 获取视频状态
        GetVideoInfoRequest statusRequest = new GetVideoInfoRequest();
        statusRequest.setVideoId(videoId);
        GetVideoInfoResponse response = client.getAcsResponse(statusRequest);
        String status = response.getVideo().getStatus();
        // status 可以是上传中，转码中，正常，转码失败等
        if ("Normal".equals(status)) {
            // 正常的情况，直接返回自适应码率的流
            GetPlayInfoRequest request = new GetPlayInfoRequest();
            request.setVideoId(videoId);
            // request.setDefinition("AUTO");
            request.setAuthTimeout(2592000L);
            GetPlayInfoResponse playResponse = client.getAcsResponse(request);
            return playResponse.getPlayInfoList().get(0).getPlayURL();
        } else if ("TranscodeFail".equals(status)) {
            // 如果是转码失败，从源文件中获取
            GetMezzanineInfoRequest request = new GetMezzanineInfoRequest();
            request.setVideoId(videoId);
            // 源片下载地址过期时间，不设置默认30天
            request.setAuthTimeout(2592000L);
            GetMezzanineInfoResponse infoResponse = client.getAcsResponse(request);
            return infoResponse.getMezzanine().getFileURL();
        }
        return null;
    }

    /**
     * 获取源文件地址
     *
     * @param videoId
     * @return
     * @throws ClientException
     */
    @Override
    public String getSourceVideoUrl(String videoId) throws ClientException {
        // 由于视频会存在转码失败的情况，需要先获取视频状态来判断
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        // 如果是转码失败，从源文件中获取
        GetMezzanineInfoRequest request = new GetMezzanineInfoRequest();
        request.setVideoId(videoId);
        request.setAuthTimeout(60 * 60 * 24L);
        GetMezzanineInfoResponse infoResponse = client.getAcsResponse(request);
        return infoResponse.getMezzanine().getFileURL();
    }

    /**
     * 阿里云vod智能点播 获取视频信息 todo 可能有迁移需求
     *
     * @param audioId
     * @return
     * @throws ClientException
     */
    @Override
    public GetPlayInfoResponse getAudioPlayInfo(String audioId) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        GetPlayInfoRequest request = new GetPlayInfoRequest();
        request.setVideoId(audioId);
        request.setAuthTimeout(2592000L);
        return client.getAcsResponse(request);
    }

    /**
     * 阿里云vod智能点播 获取图片信息 todo 可能有迁移需求
     *
     * @param imageId
     * @return
     * @throws ClientException
     */
    @Override
    public GetImageInfoResponse getImageInfo(String imageId) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        GetImageInfoRequest request = new GetImageInfoRequest();
        request.setImageId(imageId);
        return client.getAcsResponse(request);
    }

    @Override
    public GetMezzanineInfoResponse.Mezzanine getMezzanine(String ossId) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        GetMezzanineInfoResponse response = new GetMezzanineInfoResponse();
        try {
            GetMezzanineInfoRequest request = new GetMezzanineInfoRequest();
            request.setVideoId(ossId);
            // 源片下载地址过期时间
            request.setAuthTimeout(3600L);
            response = client.getAcsResponse(request);
        } catch (Exception e) {
            System.out.print("ErrorMessage = " + e.getLocalizedMessage());
        }
        return response.getMezzanine();
    }

    @Override
    public ListLiveRecordVideoResponse listLiveRecordVideo(ListLiveRecordVideoSearchDTO searchDTO) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        ListLiveRecordVideoRequest request = new ListLiveRecordVideoRequest();
        request.setActionName(LIST_LIVE_RECORD_ACTION);
        request.setAppName(searchDTO.getAppName());
        request.setDomainName(searchDTO.getDomainName());
        request.setStreamName(searchDTO.getStreamName());
        request.setPageNo(searchDTO.getPageNo());
        request.setPageSize(searchDTO.getPageSize());
        if (Objects.nonNull(searchDTO.getStartTime())) {
            request.setStartTime(searchDTO.getStartTime().format(DateTimeFormatter.ofPattern(UTC_TIME_PATTERN)));
        }
        if (Objects.nonNull(searchDTO.getEndTime())) {
            request.setEndTime(searchDTO.getEndTime().format(DateTimeFormatter.ofPattern(UTC_TIME_PATTERN)));
        }
        return client.getAcsResponse(request);
    }

    @Override
    public GetVideoListResponse vodPageSearch(AliyunVodPageSearchDTO dto) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        GetVideoListRequest request = new GetVideoListRequest();
        request.setStartTime(dto.getStartTime());
        request.setEndTime(dto.getEndTime());
        request.setPageNo(dto.getPageNo());
        request.setPageSize(dto.getPageSize());
        request.setSortBy(dto.getSortBy());
        request.setStatus(dto.getStatus());

        try {
            GetVideoListResponse response = client.getAcsResponse(request);
            return response;
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            System.out.println("ErrCode:" + e.getErrCode());
            System.out.println("ErrMsg:" + e.getErrMsg());
            System.out.println("RequestId:" + e.getRequestId());
        }
        return null;
    }

    @Override
    public boolean deleteMezzanines(List<String> videoIdList) {
        if (CollectionUtils.isEmpty(videoIdList)) {
            return true;
        }

        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);

        DeleteMezzaninesRequest request = new DeleteMezzaninesRequest();
        request.setVideoIds(String.join(",", videoIdList));
        request.setForce(false);

        try {
            DeleteMezzaninesResponse response = client.getAcsResponse(request);
            System.out.println(new Gson().toJson(response));
            return true;
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            System.out.println("ErrCode:" + e.getErrCode());
            System.out.println("ErrMsg:" + e.getErrMsg());
            System.out.println("RequestId:" + e.getRequestId());
        }
        return false;
    }

    @Override
    public VodVideoPlayDTO getVideoPlayUrlNew(String videoId) throws ClientException {
        // 由于视频会存在转码失败的情况，需要先获取视频状态来判断
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);
        // 获取视频状态
        String status = this.getVideoStatus(videoId);
        // status 可以是上传中，转码中，正常，转码失败等
        String url = null;
        String duration = null;
        if ("Normal".equals(status)) {
            // 正常的情况，直接返回自适应码率的流
            GetPlayInfoRequest request = new GetPlayInfoRequest();
            request.setVideoId(videoId);
            request.setAuthTimeout(2592000L);
            GetPlayInfoResponse playResponse = client.getAcsResponse(request);
            List<GetPlayInfoResponse.PlayInfo> playInfoList = playResponse.getPlayInfoList();
            url = playInfoList.get(0).getPlayURL();
            duration = playInfoList.get(0).getDuration();
        } else {
            // 如果是转码失败，从源文件中获取
            GetMezzanineInfoRequest request = new GetMezzanineInfoRequest();
            request.setVideoId(videoId);
            // 源片下载地址过期时间，不设置默认30天
            request.setAuthTimeout("TranscodeFail".equals(status) ? 60 * 60 * 24 * 30L : 60 * 60 * 24L);
            GetMezzanineInfoResponse infoResponse = client.getAcsResponse(request);
            url = infoResponse.getMezzanine().getFileURL();
            duration = infoResponse.getMezzanine().getDuration();
        }
        VodVideoPlayDTO dto = new VodVideoPlayDTO();
        dto.setVideoId(videoId);
        dto.setUrl(url);
        dto.setStatus(status);
        dto.setDuration(duration);
        return dto;
    }

    @Override
    public UploadMediaByURLResponse uploadMediaByURL(AliyunUploadMediaByURLDTO dto) throws Exception {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultAcsClient client = new DefaultAcsClient(profile);

        UploadMediaByURLRequest request = new UploadMediaByURLRequest();
        String encodeUrl = URLEncoder.encode(dto.getSourceUrl(), "UTF-8");
        // 视频源文件URL
        request.setUploadURLs(encodeUrl);

        // 上传视频元数据信息
        JSONObject uploadMetadata = new JSONObject();
        // 需要上传的视频源文件URL，与UploadURLs里的URL匹配才能生效
        uploadMetadata.put("SourceUrl", encodeUrl);
        // 视频标题
        uploadMetadata.put("Title", dto.getTitle());

        JSONArray uploadMetadataList = new JSONArray();
        uploadMetadataList.add(uploadMetadata);
        request.setUploadMetadatas(uploadMetadataList.toJSONString());

        // UserData，用户自定义设置参数，用户需要单独回调URL及数据透传时设置（非必须）
        JSONObject userData = new JSONObject();

        // UserData回调部分设置
        // 消息回调设置，指定时以此为准，否则以全局设置的事件通知为准
        /*
         * JSONObject messageCallback = new JSONObject(); // 设置回调地址 messageCallback.put("CallbackURL", "http://192.168.0.0/16"); // 设置回调类型，默认为http messageCallback.put("CallbackType", "http"); userData.put("MessageCallback", messageCallback.toJSONString());
         */

        JSONObject extend = new JSONObject();
        extend.put("extendType", dto.getExtendType());
        extend.put("extendId", dto.getExtendId());
        userData.put("Extend", extend.toJSONString());

        request.setUserData(userData.toJSONString());

        return client.getAcsResponse(request);
    }
}
