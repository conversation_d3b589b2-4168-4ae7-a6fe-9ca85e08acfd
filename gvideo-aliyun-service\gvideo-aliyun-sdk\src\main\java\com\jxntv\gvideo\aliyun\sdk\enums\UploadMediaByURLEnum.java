package com.jxntv.gvideo.aliyun.sdk.enums;

/**
 * Created on 2020-03-04
 */
public enum UploadMediaByURLEnum {

    RED_ACTIVITY(1, "红色回声"),
    ;

    private int value;
    private String desc;

    UploadMediaByURLEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static UploadMediaByURLEnum getTypeByValue(int value) {
        for (UploadMediaByURLEnum enums : UploadMediaByURLEnum.values()) {
            if (enums.getValue() == value) {
                return enums;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
